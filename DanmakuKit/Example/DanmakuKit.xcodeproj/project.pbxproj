// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		0F86ADA94E489E311FF27E0B /* Pods_DanmakuKit_Tests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1D101944CD8F2023168DF50E /* Pods_DanmakuKit_Tests.framework */; };
		2BAA882F2B6754AC00276535 /* PlayerExampleView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BAA882E2B6754AC00276535 /* PlayerExampleView.swift */; };
		2BAA88332B6755B300276535 /* VideoPlayerViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BAA88312B6755B300276535 /* VideoPlayerViewController.swift */; };
		2BAA88342B6755B300276535 /* VideoPlayerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BAA88322B6755B300276535 /* VideoPlayerView.swift */; };
		2BAA88362B67569000276535 /* PlayerComponent.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BAA88352B67569000276535 /* PlayerComponent.swift */; };
		2BAA88382B6756D400276535 /* Component.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BAA88372B6756D400276535 /* Component.swift */; };
		2BAA883A2B67570100276535 /* PlayerComponentRegister.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BAA88392B67570100276535 /* PlayerComponentRegister.swift */; };
		2BAA883F2B67A01300276535 /* PlayerCenterProgressView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BAA883C2B67A01300276535 /* PlayerCenterProgressView.swift */; };
		2BAA88402B67A01300276535 /* VideoPlayerProgressView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BAA883D2B67A01300276535 /* VideoPlayerProgressView.swift */; };
		2BAA88412B67A01300276535 /* PlayerControlComponent.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BAA883E2B67A01300276535 /* PlayerControlComponent.swift */; };
		2BAA88442B67A0C400276535 /* Utils.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BAA88432B67A0C400276535 /* Utils.swift */; };
		2BAA88462B67A15500276535 /* GradientView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BAA88452B67A15500276535 /* GradientView.swift */; };
		2BAA88482B67A18100276535 /* AppColor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BAA88472B67A18100276535 /* AppColor.swift */; };
		2BAA884A2B67A58A00276535 /* ExampleView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BAA88492B67A58A00276535 /* ExampleView.swift */; };
		2BAA884D2B68916000276535 /* DanmakuComponent.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BAA884C2B68916000276535 /* DanmakuComponent.swift */; };
		4EB8A543E94068F6F5D0D547 /* Pods_DanmakuKit_Example.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 26D2089B0AB2D8D386FC641F /* Pods_DanmakuKit_Example.framework */; };
		607FACD61AFB9204008FA782 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 607FACD51AFB9204008FA782 /* AppDelegate.swift */; };
		607FACD81AFB9204008FA782 /* ViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 607FACD71AFB9204008FA782 /* ViewController.swift */; };
		607FACDB1AFB9204008FA782 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 607FACD91AFB9204008FA782 /* Main.storyboard */; };
		607FACDD1AFB9204008FA782 /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 607FACDC1AFB9204008FA782 /* Images.xcassets */; };
		607FACE01AFB9204008FA782 /* LaunchScreen.xib in Resources */ = {isa = PBXBuildFile; fileRef = 607FACDE1AFB9204008FA782 /* LaunchScreen.xib */; };
		607FACEC1AFB9204008FA782 /* Tests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 607FACEB1AFB9204008FA782 /* Tests.swift */; };
		A02A4FDD26DE79F900B5BD01 /* TestDanmakuCellModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = A02A4FDC26DE79F900B5BD01 /* TestDanmakuCellModel.swift */; };
		A02A4FE126DE7A0100B5BD01 /* test.gif in Resources */ = {isa = PBXBuildFile; fileRef = A02A4FE026DE7A0100B5BD01 /* test.gif */; };
		A02A4FE526DE7A1400B5BD01 /* DanmakuTestGifCellModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = A02A4FE426DE7A1400B5BD01 /* DanmakuTestGifCellModel.swift */; };
		A076E08324FA80B200A8FC06 /* DanmakuTextCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = A076E08224FA80B200A8FC06 /* DanmakuTextCell.swift */; };
		A076E08524FA80C800A8FC06 /* DanmakuTextCellModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = A076E08424FA80C800A8FC06 /* DanmakuTextCellModel.swift */; };
		A076E08724FB79B300A8FC06 /* FunctionDemoViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = A076E08624FB79B300A8FC06 /* FunctionDemoViewController.swift */; };
		A0A0EB5925256B880078080E /* demo.MOV in Resources */ = {isa = PBXBuildFile; fileRef = A0A0EB5825256B880078080E /* demo.MOV */; };
		A0A0EB5B252896C00078080E /* danmaku.json in Resources */ = {isa = PBXBuildFile; fileRef = A0A0EB5A252896C00078080E /* danmaku.json */; };
		A0A0EB5D252896D00078080E /* DanmakuService.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0A0EB5C252896D00078080E /* DanmakuService.swift */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		607FACE61AFB9204008FA782 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 607FACC81AFB9204008FA782 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 607FACCF1AFB9204008FA782;
			remoteInfo = DanmakuKit;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		1D101944CD8F2023168DF50E /* Pods_DanmakuKit_Tests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_DanmakuKit_Tests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		1DCAB33335F0AD2638CF0E0D /* DanmakuKit.podspec */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; name = DanmakuKit.podspec; path = ../DanmakuKit.podspec; sourceTree = "<group>"; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		1FA9FDB9BDA6785F330F9ACD /* Pods-DanmakuKit_Tests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-DanmakuKit_Tests.release.xcconfig"; path = "Target Support Files/Pods-DanmakuKit_Tests/Pods-DanmakuKit_Tests.release.xcconfig"; sourceTree = "<group>"; };
		26D2089B0AB2D8D386FC641F /* Pods_DanmakuKit_Example.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_DanmakuKit_Example.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		2BAA882E2B6754AC00276535 /* PlayerExampleView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PlayerExampleView.swift; sourceTree = "<group>"; };
		2BAA88312B6755B300276535 /* VideoPlayerViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = VideoPlayerViewController.swift; sourceTree = "<group>"; };
		2BAA88322B6755B300276535 /* VideoPlayerView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = VideoPlayerView.swift; sourceTree = "<group>"; };
		2BAA88352B67569000276535 /* PlayerComponent.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PlayerComponent.swift; sourceTree = "<group>"; };
		2BAA88372B6756D400276535 /* Component.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Component.swift; sourceTree = "<group>"; };
		2BAA88392B67570100276535 /* PlayerComponentRegister.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PlayerComponentRegister.swift; sourceTree = "<group>"; };
		2BAA883C2B67A01300276535 /* PlayerCenterProgressView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PlayerCenterProgressView.swift; sourceTree = "<group>"; };
		2BAA883D2B67A01300276535 /* VideoPlayerProgressView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = VideoPlayerProgressView.swift; sourceTree = "<group>"; };
		2BAA883E2B67A01300276535 /* PlayerControlComponent.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PlayerControlComponent.swift; sourceTree = "<group>"; };
		2BAA88432B67A0C400276535 /* Utils.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Utils.swift; sourceTree = "<group>"; };
		2BAA88452B67A15500276535 /* GradientView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GradientView.swift; sourceTree = "<group>"; };
		2BAA88472B67A18100276535 /* AppColor.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AppColor.swift; sourceTree = "<group>"; };
		2BAA88492B67A58A00276535 /* ExampleView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ExampleView.swift; sourceTree = "<group>"; };
		2BAA884C2B68916000276535 /* DanmakuComponent.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DanmakuComponent.swift; sourceTree = "<group>"; };
		50995B1A95020F1ED52DF199 /* Pods-DanmakuKit_Example.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-DanmakuKit_Example.release.xcconfig"; path = "Target Support Files/Pods-DanmakuKit_Example/Pods-DanmakuKit_Example.release.xcconfig"; sourceTree = "<group>"; };
		607FACD01AFB9204008FA782 /* DanmakuKit_Example.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = DanmakuKit_Example.app; sourceTree = BUILT_PRODUCTS_DIR; };
		607FACD41AFB9204008FA782 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		607FACD51AFB9204008FA782 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		607FACD71AFB9204008FA782 /* ViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ViewController.swift; sourceTree = "<group>"; };
		607FACDA1AFB9204008FA782 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		607FACDC1AFB9204008FA782 /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Images.xcassets; sourceTree = "<group>"; };
		607FACDF1AFB9204008FA782 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.xib; name = Base; path = Base.lproj/LaunchScreen.xib; sourceTree = "<group>"; };
		607FACE51AFB9204008FA782 /* DanmakuKit_Tests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = DanmakuKit_Tests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		607FACEA1AFB9204008FA782 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		607FACEB1AFB9204008FA782 /* Tests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Tests.swift; sourceTree = "<group>"; };
		A02A4FDC26DE79F900B5BD01 /* TestDanmakuCellModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = TestDanmakuCellModel.swift; sourceTree = "<group>"; };
		A02A4FE026DE7A0100B5BD01 /* test.gif */ = {isa = PBXFileReference; lastKnownFileType = image.gif; path = test.gif; sourceTree = "<group>"; };
		A02A4FE426DE7A1400B5BD01 /* DanmakuTestGifCellModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DanmakuTestGifCellModel.swift; sourceTree = "<group>"; };
		A076E08224FA80B200A8FC06 /* DanmakuTextCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DanmakuTextCell.swift; sourceTree = "<group>"; };
		A076E08424FA80C800A8FC06 /* DanmakuTextCellModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DanmakuTextCellModel.swift; sourceTree = "<group>"; };
		A076E08624FB79B300A8FC06 /* FunctionDemoViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FunctionDemoViewController.swift; sourceTree = "<group>"; };
		A0A0EB5825256B880078080E /* demo.MOV */ = {isa = PBXFileReference; lastKnownFileType = video.quicktime; path = demo.MOV; sourceTree = "<group>"; };
		A0A0EB5A252896C00078080E /* danmaku.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = danmaku.json; sourceTree = "<group>"; };
		A0A0EB5C252896D00078080E /* DanmakuService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DanmakuService.swift; sourceTree = "<group>"; };
		AA16C3617F09A2E85304E8B9 /* README.md */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = net.daringfireball.markdown; name = README.md; path = ../README.md; sourceTree = "<group>"; };
		CA5BFE3043DC2B7C09EDCE06 /* Pods-DanmakuKit_Tests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-DanmakuKit_Tests.debug.xcconfig"; path = "Target Support Files/Pods-DanmakuKit_Tests/Pods-DanmakuKit_Tests.debug.xcconfig"; sourceTree = "<group>"; };
		CDB672DA59492FFD0B896F83 /* Pods-DanmakuKit_Example.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-DanmakuKit_Example.debug.xcconfig"; path = "Target Support Files/Pods-DanmakuKit_Example/Pods-DanmakuKit_Example.debug.xcconfig"; sourceTree = "<group>"; };
		D633F0E376FE4F100184B4D4 /* LICENSE */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; name = LICENSE; path = ../LICENSE; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		607FACCD1AFB9204008FA782 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				4EB8A543E94068F6F5D0D547 /* Pods_DanmakuKit_Example.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		607FACE21AFB9204008FA782 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				0F86ADA94E489E311FF27E0B /* Pods_DanmakuKit_Tests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		2BAA882D2B67548D00276535 /* SwiftUI */ = {
			isa = PBXGroup;
			children = (
				2BAA882E2B6754AC00276535 /* PlayerExampleView.swift */,
				2BAA88492B67A58A00276535 /* ExampleView.swift */,
			);
			name = SwiftUI;
			sourceTree = "<group>";
		};
		2BAA88302B67557E00276535 /* Player */ = {
			isa = PBXGroup;
			children = (
				2BAA884B2B68914400276535 /* Danmaku */,
				2BAA88422B67A01F00276535 /* Foundation */,
				2BAA883B2B67A01300276535 /* Control */,
				2BAA88322B6755B300276535 /* VideoPlayerView.swift */,
				2BAA88312B6755B300276535 /* VideoPlayerViewController.swift */,
				2BAA88392B67570100276535 /* PlayerComponentRegister.swift */,
			);
			name = Player;
			sourceTree = "<group>";
		};
		2BAA883B2B67A01300276535 /* Control */ = {
			isa = PBXGroup;
			children = (
				2BAA883C2B67A01300276535 /* PlayerCenterProgressView.swift */,
				2BAA883D2B67A01300276535 /* VideoPlayerProgressView.swift */,
				2BAA883E2B67A01300276535 /* PlayerControlComponent.swift */,
				2BAA88452B67A15500276535 /* GradientView.swift */,
			);
			path = Control;
			sourceTree = "<group>";
		};
		2BAA88422B67A01F00276535 /* Foundation */ = {
			isa = PBXGroup;
			children = (
				2BAA88352B67569000276535 /* PlayerComponent.swift */,
				2BAA88372B6756D400276535 /* Component.swift */,
				2BAA88432B67A0C400276535 /* Utils.swift */,
			);
			path = Foundation;
			sourceTree = "<group>";
		};
		2BAA884B2B68914400276535 /* Danmaku */ = {
			isa = PBXGroup;
			children = (
				2BAA884C2B68916000276535 /* DanmakuComponent.swift */,
			);
			path = Danmaku;
			sourceTree = "<group>";
		};
		607FACC71AFB9204008FA782 = {
			isa = PBXGroup;
			children = (
				607FACF51AFB993E008FA782 /* Podspec Metadata */,
				607FACD21AFB9204008FA782 /* Example for DanmakuKit */,
				607FACE81AFB9204008FA782 /* Tests */,
				607FACD11AFB9204008FA782 /* Products */,
				6A554320B78BF2F1990155A0 /* Pods */,
				A02F98C151BA311AB3A9E879 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		607FACD11AFB9204008FA782 /* Products */ = {
			isa = PBXGroup;
			children = (
				607FACD01AFB9204008FA782 /* DanmakuKit_Example.app */,
				607FACE51AFB9204008FA782 /* DanmakuKit_Tests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		607FACD21AFB9204008FA782 /* Example for DanmakuKit */ = {
			isa = PBXGroup;
			children = (
				2BAA88302B67557E00276535 /* Player */,
				2BAA882D2B67548D00276535 /* SwiftUI */,
				607FACD51AFB9204008FA782 /* AppDelegate.swift */,
				607FACD71AFB9204008FA782 /* ViewController.swift */,
				607FACD91AFB9204008FA782 /* Main.storyboard */,
				607FACDC1AFB9204008FA782 /* Images.xcassets */,
				607FACDE1AFB9204008FA782 /* LaunchScreen.xib */,
				607FACD31AFB9204008FA782 /* Supporting Files */,
				A076E08224FA80B200A8FC06 /* DanmakuTextCell.swift */,
				A076E08424FA80C800A8FC06 /* DanmakuTextCellModel.swift */,
				A076E08624FB79B300A8FC06 /* FunctionDemoViewController.swift */,
				A0A0EB5C252896D00078080E /* DanmakuService.swift */,
				A02A4FDC26DE79F900B5BD01 /* TestDanmakuCellModel.swift */,
				A02A4FE426DE7A1400B5BD01 /* DanmakuTestGifCellModel.swift */,
				2BAA88472B67A18100276535 /* AppColor.swift */,
				A0A0EB5E252896D80078080E /* Resource */,
			);
			name = "Example for DanmakuKit";
			path = DanmakuKit;
			sourceTree = "<group>";
		};
		607FACD31AFB9204008FA782 /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				607FACD41AFB9204008FA782 /* Info.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		607FACE81AFB9204008FA782 /* Tests */ = {
			isa = PBXGroup;
			children = (
				607FACEB1AFB9204008FA782 /* Tests.swift */,
				607FACE91AFB9204008FA782 /* Supporting Files */,
			);
			path = Tests;
			sourceTree = "<group>";
		};
		607FACE91AFB9204008FA782 /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				607FACEA1AFB9204008FA782 /* Info.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		607FACF51AFB993E008FA782 /* Podspec Metadata */ = {
			isa = PBXGroup;
			children = (
				1DCAB33335F0AD2638CF0E0D /* DanmakuKit.podspec */,
				AA16C3617F09A2E85304E8B9 /* README.md */,
				D633F0E376FE4F100184B4D4 /* LICENSE */,
			);
			name = "Podspec Metadata";
			sourceTree = "<group>";
		};
		6A554320B78BF2F1990155A0 /* Pods */ = {
			isa = PBXGroup;
			children = (
				CDB672DA59492FFD0B896F83 /* Pods-DanmakuKit_Example.debug.xcconfig */,
				50995B1A95020F1ED52DF199 /* Pods-DanmakuKit_Example.release.xcconfig */,
				CA5BFE3043DC2B7C09EDCE06 /* Pods-DanmakuKit_Tests.debug.xcconfig */,
				1FA9FDB9BDA6785F330F9ACD /* Pods-DanmakuKit_Tests.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		A02F98C151BA311AB3A9E879 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				26D2089B0AB2D8D386FC641F /* Pods_DanmakuKit_Example.framework */,
				1D101944CD8F2023168DF50E /* Pods_DanmakuKit_Tests.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		A0A0EB5E252896D80078080E /* Resource */ = {
			isa = PBXGroup;
			children = (
				A02A4FE026DE7A0100B5BD01 /* test.gif */,
				A0A0EB5A252896C00078080E /* danmaku.json */,
				A0A0EB5825256B880078080E /* demo.MOV */,
			);
			name = Resource;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		607FACCF1AFB9204008FA782 /* DanmakuKit_Example */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 607FACEF1AFB9204008FA782 /* Build configuration list for PBXNativeTarget "DanmakuKit_Example" */;
			buildPhases = (
				C9A1436FAFE6F0AF53D790C6 /* [CP] Check Pods Manifest.lock */,
				607FACCC1AFB9204008FA782 /* Sources */,
				607FACCD1AFB9204008FA782 /* Frameworks */,
				607FACCE1AFB9204008FA782 /* Resources */,
				D5846877269A76B983B3FCC4 /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = DanmakuKit_Example;
			productName = DanmakuKit;
			productReference = 607FACD01AFB9204008FA782 /* DanmakuKit_Example.app */;
			productType = "com.apple.product-type.application";
		};
		607FACE41AFB9204008FA782 /* DanmakuKit_Tests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 607FACF21AFB9204008FA782 /* Build configuration list for PBXNativeTarget "DanmakuKit_Tests" */;
			buildPhases = (
				84690B04C17867BFB23DB4DA /* [CP] Check Pods Manifest.lock */,
				607FACE11AFB9204008FA782 /* Sources */,
				607FACE21AFB9204008FA782 /* Frameworks */,
				607FACE31AFB9204008FA782 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				607FACE71AFB9204008FA782 /* PBXTargetDependency */,
			);
			name = DanmakuKit_Tests;
			productName = Tests;
			productReference = 607FACE51AFB9204008FA782 /* DanmakuKit_Tests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		607FACC81AFB9204008FA782 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 0830;
				LastUpgradeCheck = 1150;
				ORGANIZATIONNAME = CocoaPods;
				TargetAttributes = {
					607FACCF1AFB9204008FA782 = {
						CreatedOnToolsVersion = 6.3.1;
						DevelopmentTeam = DGHUKDH43Z;
						LastSwiftMigration = 0900;
					};
					607FACE41AFB9204008FA782 = {
						CreatedOnToolsVersion = 6.3.1;
						DevelopmentTeam = 437JAL24TE;
						LastSwiftMigration = 0900;
						TestTargetID = 607FACCF1AFB9204008FA782;
					};
				};
			};
			buildConfigurationList = 607FACCB1AFB9204008FA782 /* Build configuration list for PBXProject "DanmakuKit" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				English,
				en,
				Base,
			);
			mainGroup = 607FACC71AFB9204008FA782;
			productRefGroup = 607FACD11AFB9204008FA782 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				607FACCF1AFB9204008FA782 /* DanmakuKit_Example */,
				607FACE41AFB9204008FA782 /* DanmakuKit_Tests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		607FACCE1AFB9204008FA782 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				607FACDB1AFB9204008FA782 /* Main.storyboard in Resources */,
				A0A0EB5925256B880078080E /* demo.MOV in Resources */,
				607FACE01AFB9204008FA782 /* LaunchScreen.xib in Resources */,
				A0A0EB5B252896C00078080E /* danmaku.json in Resources */,
				A02A4FE126DE7A0100B5BD01 /* test.gif in Resources */,
				607FACDD1AFB9204008FA782 /* Images.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		607FACE31AFB9204008FA782 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		84690B04C17867BFB23DB4DA /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-DanmakuKit_Tests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		C9A1436FAFE6F0AF53D790C6 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-DanmakuKit_Example-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		D5846877269A76B983B3FCC4 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-DanmakuKit_Example/Pods-DanmakuKit_Example-frameworks.sh",
				"${BUILT_PRODUCTS_DIR}/DanmakuKit/DanmakuKit.framework",
				"${BUILT_PRODUCTS_DIR}/DeviceKit/DeviceKit.framework",
				"${BUILT_PRODUCTS_DIR}/SwiftyJSON/SwiftyJSON.framework",
			);
			name = "[CP] Embed Pods Frameworks";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/DanmakuKit.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/DeviceKit.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/SwiftyJSON.framework",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-DanmakuKit_Example/Pods-DanmakuKit_Example-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		607FACCC1AFB9204008FA782 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				2BAA88362B67569000276535 /* PlayerComponent.swift in Sources */,
				2BAA88462B67A15500276535 /* GradientView.swift in Sources */,
				2BAA88332B6755B300276535 /* VideoPlayerViewController.swift in Sources */,
				2BAA88412B67A01300276535 /* PlayerControlComponent.swift in Sources */,
				2BAA88482B67A18100276535 /* AppColor.swift in Sources */,
				A0A0EB5D252896D00078080E /* DanmakuService.swift in Sources */,
				A076E08524FA80C800A8FC06 /* DanmakuTextCellModel.swift in Sources */,
				2BAA88402B67A01300276535 /* VideoPlayerProgressView.swift in Sources */,
				2BAA884A2B67A58A00276535 /* ExampleView.swift in Sources */,
				A076E08724FB79B300A8FC06 /* FunctionDemoViewController.swift in Sources */,
				607FACD81AFB9204008FA782 /* ViewController.swift in Sources */,
				607FACD61AFB9204008FA782 /* AppDelegate.swift in Sources */,
				2BAA883A2B67570100276535 /* PlayerComponentRegister.swift in Sources */,
				A076E08324FA80B200A8FC06 /* DanmakuTextCell.swift in Sources */,
				A02A4FDD26DE79F900B5BD01 /* TestDanmakuCellModel.swift in Sources */,
				2BAA883F2B67A01300276535 /* PlayerCenterProgressView.swift in Sources */,
				A02A4FE526DE7A1400B5BD01 /* DanmakuTestGifCellModel.swift in Sources */,
				2BAA88442B67A0C400276535 /* Utils.swift in Sources */,
				2BAA88382B6756D400276535 /* Component.swift in Sources */,
				2BAA882F2B6754AC00276535 /* PlayerExampleView.swift in Sources */,
				2BAA884D2B68916000276535 /* DanmakuComponent.swift in Sources */,
				2BAA88342B6755B300276535 /* VideoPlayerView.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		607FACE11AFB9204008FA782 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				607FACEC1AFB9204008FA782 /* Tests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		607FACE71AFB9204008FA782 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 607FACCF1AFB9204008FA782 /* DanmakuKit_Example */;
			targetProxy = 607FACE61AFB9204008FA782 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		607FACD91AFB9204008FA782 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				607FACDA1AFB9204008FA782 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		607FACDE1AFB9204008FA782 /* LaunchScreen.xib */ = {
			isa = PBXVariantGroup;
			children = (
				607FACDF1AFB9204008FA782 /* Base */,
			);
			name = LaunchScreen.xib;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		607FACED1AFB9204008FA782 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 9.3;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		607FACEE1AFB9204008FA782 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 9.3;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SWIFT_OPTIMIZATION_LEVEL = "-Owholemodule";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		607FACF01AFB9204008FA782 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = CDB672DA59492FFD0B896F83 /* Pods-DanmakuKit_Example.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				DEVELOPMENT_TEAM = DGHUKDH43Z;
				INFOPLIST_FILE = DanmakuKit/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				MODULE_NAME = ExampleApp;
				PRODUCT_BUNDLE_IDENTIFIER = com.sail.tcg;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_SWIFT3_OBJC_INFERENCE = Default;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		607FACF11AFB9204008FA782 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 50995B1A95020F1ED52DF199 /* Pods-DanmakuKit_Example.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				DEVELOPMENT_TEAM = DGHUKDH43Z;
				INFOPLIST_FILE = DanmakuKit/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				MODULE_NAME = ExampleApp;
				PRODUCT_BUNDLE_IDENTIFIER = com.sail.tcg;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_SWIFT3_OBJC_INFERENCE = Default;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
		607FACF31AFB9204008FA782 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = CA5BFE3043DC2B7C09EDCE06 /* Pods-DanmakuKit_Tests.debug.xcconfig */;
			buildSettings = {
				DEVELOPMENT_TEAM = 437JAL24TE;
				FRAMEWORK_SEARCH_PATHS = (
					"$(PLATFORM_DIR)/Developer/Library/Frameworks",
					"$(inherited)",
				);
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				INFOPLIST_FILE = Tests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_SWIFT3_OBJC_INFERENCE = Default;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/DanmakuKit_Example.app/DanmakuKit_Example";
			};
			name = Debug;
		};
		607FACF41AFB9204008FA782 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1FA9FDB9BDA6785F330F9ACD /* Pods-DanmakuKit_Tests.release.xcconfig */;
			buildSettings = {
				DEVELOPMENT_TEAM = 437JAL24TE;
				FRAMEWORK_SEARCH_PATHS = (
					"$(PLATFORM_DIR)/Developer/Library/Frameworks",
					"$(inherited)",
				);
				INFOPLIST_FILE = Tests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_SWIFT3_OBJC_INFERENCE = Default;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/DanmakuKit_Example.app/DanmakuKit_Example";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		607FACCB1AFB9204008FA782 /* Build configuration list for PBXProject "DanmakuKit" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				607FACED1AFB9204008FA782 /* Debug */,
				607FACEE1AFB9204008FA782 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		607FACEF1AFB9204008FA782 /* Build configuration list for PBXNativeTarget "DanmakuKit_Example" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				607FACF01AFB9204008FA782 /* Debug */,
				607FACF11AFB9204008FA782 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		607FACF21AFB9204008FA782 /* Build configuration list for PBXNativeTarget "DanmakuKit_Tests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				607FACF31AFB9204008FA782 /* Debug */,
				607FACF41AFB9204008FA782 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 607FACC81AFB9204008FA782 /* Project object */;
}
